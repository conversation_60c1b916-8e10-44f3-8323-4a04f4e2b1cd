<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Gradient background -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="0"
                android:startY="0"
                android:endX="108"
                android:endY="108"
                android:type="linear">
                <item
                    android:color="#1976D2"
                    android:offset="0.0" />
                <item
                    android:color="#2196F3"
                    android:offset="0.5" />
                <item
                    android:color="#42A5F5"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Subtle network pattern overlay -->
    <path
        android:strokeColor="#1A237E"
        android:strokeWidth="0.5"
        android:pathData="M20,20 L88,88 M88,20 L20,88"
        android:strokeAlpha="0.1" />

    <!-- Decorative dots -->
    <path
        android:fillColor="#E3F2FD"
        android:pathData="M25,25m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0"
        android:fillAlpha="0.3" />

    <path
        android:fillColor="#E3F2FD"
        android:pathData="M83,25m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0"
        android:fillAlpha="0.3" />

    <path
        android:fillColor="#E3F2FD"
        android:pathData="M25,83m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0"
        android:fillAlpha="0.3" />

    <path
        android:fillColor="#E3F2FD"
        android:pathData="M83,83m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0"
        android:fillAlpha="0.3" />

</vector>
