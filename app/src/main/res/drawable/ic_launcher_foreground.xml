<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Central hub/router -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M54,42
                         L66,42
                         A6,6 0,0 1,72 48
                         L72,60
                         A6,6 0,0 1,66 66
                         L42,66
                         A6,6 0,0 1,36 60
                         L36,48
                         A6,6 0,0 1,42 42
                         Z" />

    <!-- WiFi signal in center -->
    <path
        android:fillColor="#2196F3"
        android:strokeWidth="2"
        android:pathData="M54,54
                         L54,48
                         M48,54
                         L60,54" />

    <!-- Device nodes with different colors for status -->
    <!-- Top device (online - green) -->
    <path
        android:fillColor="#4CAF50"
        android:pathData="M54,24m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Top-right device (warning - orange) -->
    <path
        android:fillColor="#FF9800"
        android:pathData="M76,32m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Right device (online - green) -->
    <path
        android:fillColor="#4CAF50"
        android:pathData="M84,54m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Bottom-right device (error - red) -->
    <path
        android:fillColor="#F44336"
        android:pathData="M76,76m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Bottom device (online - green) -->
    <path
        android:fillColor="#4CAF50"
        android:pathData="M54,84m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Bottom-left device (warning - orange) -->
    <path
        android:fillColor="#FF9800"
        android:pathData="M32,76m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Left device (online - green) -->
    <path
        android:fillColor="#4CAF50"
        android:pathData="M24,54m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Top-left device (online - green) -->
    <path
        android:fillColor="#4CAF50"
        android:pathData="M32,32m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Connection lines -->
    <path
        android:strokeColor="#E3F2FD"
        android:strokeWidth="2.5"
        android:pathData="M54,42 L54,29
                         M54,42 L71,37
                         M72,48 L79,54
                         M72,60 L71,71
                         M54,66 L54,79
                         M42,66 L37,71
                         M36,48 L29,54
                         M42,42 L37,37" />

    <!-- Small antenna on central hub -->
    <path
        android:strokeColor="#2196F3"
        android:strokeWidth="2"
        android:pathData="M54,42 L54,36
                         M51,39 L57,39" />

    <!-- Small device icons -->
    <!-- Temperature sensor icon on top device -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M54,22 L54,26 M52,24 L56,24" />

    <!-- Lock icon on top-right device -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M76,30 L76,34 M74,32 L78,32" />

    <!-- Light bulb icon on right device -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M84,54m-2,0a2,2 0,1 1,4 0a2,2 0,1 1,-4 0" />
</vector>