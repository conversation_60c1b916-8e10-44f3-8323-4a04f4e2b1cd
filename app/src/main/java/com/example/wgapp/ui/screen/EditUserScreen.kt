package com.example.wgapp.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.wgapp.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditUserScreen(
    userViewModel: UserViewModel,
    onBackClick: () -> Unit,
    onSaveSuccess: () -> Unit
) {
    LaunchedEffect(userViewModel.isEditing) {
        if (!userViewModel.isEditing) {
            onSaveSuccess()
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        TopAppBar(
            title = { Text("编辑用户信息") },
            navigationIcon = {
                IconButton(onClick = {
                    userViewModel.cancelEditing()
                    onBackClick()
                }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(
                    onClick = { userViewModel.saveUserProfile() },
                    enabled = !userViewModel.isLoading
                ) {
                    if (userViewModel.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(Icons.Default.Save, contentDescription = "保存")
                    }
                }
            }
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = "编辑个人信息",
                        fontSize = 18.sp,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    OutlinedTextField(
                        value = userViewModel.editUsername,
                        onValueChange = userViewModel::updateEditUsername,
                        label = { Text("用户名") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !userViewModel.isLoading
                    )
                    
                    OutlinedTextField(
                        value = userViewModel.editEmail,
                        onValueChange = userViewModel::updateEditEmail,
                        label = { Text("邮箱") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                        enabled = !userViewModel.isLoading
                    )
                    
                    OutlinedTextField(
                        value = userViewModel.editPhone,
                        onValueChange = userViewModel::updateEditPhone,
                        label = { Text("手机号") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                        enabled = !userViewModel.isLoading
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedButton(
                    onClick = {
                        userViewModel.cancelEditing()
                        onBackClick()
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(50.dp),
                    enabled = !userViewModel.isLoading
                ) {
                    Text("取消")
                }
                
                Button(
                    onClick = { userViewModel.saveUserProfile() },
                    modifier = Modifier
                        .weight(1f)
                        .height(50.dp),
                    enabled = !userViewModel.isLoading
                ) {
                    if (userViewModel.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = MaterialTheme.colorScheme.onPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("保存")
                    }
                }
            }
        }
    }
}
