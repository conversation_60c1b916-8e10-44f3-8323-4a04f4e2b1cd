package com.example.wgapp.ui.components

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.padding
import com.example.wgapp.data.DeviceStatus

@Composable
fun DeviceStatusChip(status: DeviceStatus) {
    val (text, color) = when (status) {
        DeviceStatus.ONLINE -> "在线" to Color(0xFF4CAF50)
        DeviceStatus.OFFLINE -> "离线" to Color(0xFF9E9E9E)
        DeviceStatus.ERROR -> "故障" to Color(0xFFF44336)
    }
    
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = MaterialTheme.shapes.small,
        modifier = Modifier.padding(4.dp)
    ) {
        Text(
            text = text,
            color = color,
            fontSize = 12.sp,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}
