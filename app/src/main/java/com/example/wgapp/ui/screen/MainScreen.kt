package com.example.wgapp.ui.screen

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Devices
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.wgapp.navigation.Screen
import com.example.wgapp.viewmodel.AuthViewModel
import com.example.wgapp.viewmodel.DeviceViewModel
import com.example.wgapp.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    deviceViewModel: DeviceViewModel,
    userViewModel: UserViewModel,
    authViewModel: AuthViewModel,
    onDeviceClick: (String) -> Unit
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    
    Scaffold(
        bottomBar = {
            NavigationBar {
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Devices, contentDescription = "设备") },
                    label = { Text("设备") },
                    selected = currentRoute == Screen.DeviceList.route,
                    onClick = {
                        navController.navigate(Screen.DeviceList.route) {
                            popUpTo(Screen.DeviceList.route) { inclusive = true }
                        }
                    }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Person, contentDescription = "用户") },
                    label = { Text("用户") },
                    selected = currentRoute == Screen.UserProfile.route,
                    onClick = {
                        navController.navigate(Screen.UserProfile.route) {
                            popUpTo(Screen.UserProfile.route) { inclusive = true }
                        }
                    }
                )
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.DeviceList.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.DeviceList.route) {
                DeviceListScreen(
                    deviceViewModel = deviceViewModel,
                    onDeviceClick = { device ->
                        deviceViewModel.selectDevice(device)
                        onDeviceClick(device.id)
                    }
                )
            }
            
            composable(Screen.UserProfile.route) {
                UserProfileScreen(
                    userViewModel = userViewModel,
                    authViewModel = authViewModel,
                    onEditClick = {
                        userViewModel.startEditing()
                        navController.navigate(Screen.EditUser.route)
                    }
                )
            }
            
            composable(Screen.EditUser.route) {
                EditUserScreen(
                    userViewModel = userViewModel,
                    onBackClick = {
                        navController.popBackStack()
                    },
                    onSaveSuccess = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}
