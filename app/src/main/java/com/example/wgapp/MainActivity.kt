package com.example.wgapp

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.wgapp.navigation.Screen
import com.example.wgapp.ui.screen.DeviceDetailScreen
import com.example.wgapp.ui.screen.LoginScreen
import com.example.wgapp.ui.screen.MainScreen
import com.example.wgapp.ui.theme.WGAppTheme
import com.example.wgapp.viewmodel.AuthViewModel
import com.example.wgapp.viewmodel.DeviceViewModel
import com.example.wgapp.viewmodel.UserViewModel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            WGAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    IoTApp()
                }
            }
        }
    }
}

@Composable
fun IoTApp() {
    val navController = rememberNavController()
    val authViewModel: AuthViewModel = viewModel()
    val deviceViewModel: DeviceViewModel = viewModel()
    val userViewModel: UserViewModel = viewModel()

    // 监听登录状态变化
    LaunchedEffect(authViewModel.isLoggedIn) {
        if (!authViewModel.isLoggedIn) {
            navController.navigate(Screen.Login.route) {
                popUpTo(0) { inclusive = true }
            }
        }
    }

    NavHost(
        navController = navController,
        startDestination = Screen.Login.route
    ) {
        composable(Screen.Login.route) {
            LoginScreen(
                authViewModel = authViewModel,
                onLoginSuccess = {
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                }
            )
        }

        composable(Screen.Main.route) {
            MainScreen(
                deviceViewModel = deviceViewModel,
                userViewModel = userViewModel,
                authViewModel = authViewModel,
                onDeviceClick = { deviceId ->
                    navController.navigate(Screen.DeviceDetail.createRoute(deviceId))
                }
            )
        }

        composable(Screen.DeviceDetail.route) { backStackEntry ->
            val deviceId = backStackEntry.arguments?.getString("deviceId")
            val device = deviceViewModel.devices.find { it.id == deviceId }
            DeviceDetailScreen(
                device = device,
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}