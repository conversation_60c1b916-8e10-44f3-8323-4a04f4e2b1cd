package com.example.wgapp.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import com.example.wgapp.data.Device
import com.example.wgapp.data.DeviceStatus

class DeviceViewModel : ViewModel() {
    var devices by mutableStateOf(emptyList<Device>())
        private set
    
    var selectedDevice by mutableStateOf<Device?>(null)
        private set
    
    var isLoading by mutableStateOf(false)
        private set
    
    init {
        loadDevices()
    }
    
    private fun loadDevices() {
        isLoading = true
        // 模拟加载设备数据
        devices = listOf(
            Device(
                id = "1",
                name = "智能温湿度传感器",
                type = "传感器",
                status = DeviceStatus.ONLINE,
                location = "客厅",
                lastOnline = "2024-01-15 14:30",
                temperature = 23.5f,
                humidity = 65.2f,
                batteryLevel = 85
            ),
            Device(
                id = "2",
                name = "智能门锁",
                type = "安防设备",
                status = DeviceStatus.ONLINE,
                location = "大门",
                lastOnline = "2024-01-15 14:25",
                batteryLevel = 92
            ),
            Device(
                id = "3",
                name = "智能灯泡",
                type = "照明设备",
                status = DeviceStatus.OFFLINE,
                location = "卧室",
                lastOnline = "2024-01-15 10:15"
            ),
            Device(
                id = "4",
                name = "空气质量监测器",
                type = "传感器",
                status = DeviceStatus.ERROR,
                location = "书房",
                lastOnline = "2024-01-15 09:45",
                temperature = 22.1f,
                humidity = 58.7f,
                batteryLevel = 15
            )
        )
        isLoading = false
    }
    
    fun selectDevice(device: Device) {
        selectedDevice = device
    }
    
    fun refreshDevices() {
        loadDevices()
    }
}
