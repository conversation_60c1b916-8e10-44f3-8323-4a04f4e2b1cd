package com.example.wgapp.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import com.example.wgapp.data.User

class UserViewModel : ViewModel() {
    var user by mutableStateOf<User?>(null)
        private set
    
    var isEditing by mutableStateOf(false)
        private set
    
    var editUsername by mutableStateOf("")
        private set
    
    var editEmail by mutableStateOf("")
        private set
    
    var editPhone by mutableStateOf("")
        private set
    
    var isLoading by mutableStateOf(false)
        private set
    
    init {
        loadUserProfile()
    }
    
    private fun loadUserProfile() {
        isLoading = true
        // 模拟加载用户数据
        user = User(
            id = "user_001",
            username = "admin",
            email = "<EMAIL>",
            phone = "13800138000",
            avatar = null,
            createdAt = "2024-01-01"
        )
        isLoading = false
    }
    
    fun startEditing() {
        user?.let {
            editUsername = it.username
            editEmail = it.email
            editPhone = it.phone
            isEditing = true
        }
    }
    
    fun cancelEditing() {
        isEditing = false
        editUsername = ""
        editEmail = ""
        editPhone = ""
    }
    
    fun updateEditUsername(newUsername: String) {
        editUsername = newUsername
    }
    
    fun updateEditEmail(newEmail: String) {
        editEmail = newEmail
    }
    
    fun updateEditPhone(newPhone: String) {
        editPhone = newPhone
    }
    
    fun saveUserProfile() {
        isLoading = true
        // 模拟保存用户数据
        user = user?.copy(
            username = editUsername,
            email = editEmail,
            phone = editPhone
        )
        isEditing = false
        isLoading = false
    }
}
