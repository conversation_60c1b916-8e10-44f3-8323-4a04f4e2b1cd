package com.example.wgapp.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue

class AuthViewModel : ViewModel() {
    var username by mutableStateOf("")
        private set
    
    var password by mutableStateOf("")
        private set
    
    var isLoading by mutableStateOf(false)
        private set
    
    var isLoggedIn by mutableStateOf(false)
        private set
    
    var errorMessage by mutableStateOf<String?>(null)
        private set
    
    fun updateUsername(newUsername: String) {
        username = newUsername
        errorMessage = null
    }
    
    fun updatePassword(newPassword: String) {
        password = newPassword
        errorMessage = null
    }
    
    fun login() {
        if (username.isBlank() || password.isBlank()) {
            errorMessage = "用户名和密码不能为空"
            return
        }
        
        isLoading = true
        errorMessage = null
        
        // 模拟登录过程
        // 在实际应用中，这里应该调用API
        if (username == "admin" && password == "123456") {
            isLoggedIn = true
            isLoading = false
        } else {
            errorMessage = "用户名或密码错误"
            isLoading = false
        }
    }
    
    fun logout() {
        isLoggedIn = false
        username = ""
        password = ""
        errorMessage = null
    }
}
