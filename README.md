# 物联网设备管理 APP

这是一个使用 Jetpack Compose 开发的物联网设备管理应用程序。

## 功能特性

### 🔐 用户认证
- 登录页面，支持用户名和密码登录
- 测试账号：`admin` / `123456`
- 登录状态管理

### 📱 主界面
- 底部导航栏，包含两个主要 Tab：
  - **设备列表**：显示所有物联网设备
  - **用户信息**：显示和管理用户个人信息

### 🔧 设备管理
- **设备列表页面**：
  - 显示设备名称、类型、位置、状态
  - 设备状态指示器（在线/离线/故障）
  - 电池电量显示
  - 下拉刷新功能
  - 点击设备进入详情页面

- **设备详情页面**：
  - 设备基本信息（名称、类型、位置、状态）
  - 传感器数据（温度、湿度）
  - 电池信息（电量百分比和进度条）
  - 返回按钮

### 👤 用户管理
- **用户信息页面**：
  - 显示用户头像、用户名、邮箱、手机号
  - 编辑按钮
  - 退出登录功能

- **编辑用户信息页面**：
  - 可编辑用户名、邮箱、手机号
  - 保存和取消功能
  - 表单验证

## 技术栈

- **UI 框架**：Jetpack Compose
- **导航**：Navigation Compose
- **状态管理**：ViewModel + Compose State
- **架构模式**：MVVM
- **UI 组件**：Material Design 3

## 项目结构

```
app/src/main/java/com/example/wgapp/
├── data/                          # 数据模型
│   ├── Device.kt                  # 设备数据模型
│   └── User.kt                    # 用户数据模型
├── navigation/                    # 导航相关
│   └── Screen.kt                  # 路由定义
├── ui/
│   ├── components/                # 可复用组件
│   │   └── DeviceStatusChip.kt    # 设备状态指示器
│   ├── screen/                    # 页面组件
│   │   ├── LoginScreen.kt         # 登录页面
│   │   ├── MainScreen.kt          # 主界面（底部导航）
│   │   ├── DeviceListScreen.kt    # 设备列表页面
│   │   ├── DeviceDetailScreen.kt  # 设备详情页面
│   │   ├── UserProfileScreen.kt   # 用户信息页面
│   │   └── EditUserScreen.kt      # 编辑用户信息页面
│   └── theme/                     # 主题相关
├── viewmodel/                     # 视图模型
│   ├── AuthViewModel.kt           # 认证状态管理
│   ├── DeviceViewModel.kt         # 设备数据管理
│   └── UserViewModel.kt           # 用户数据管理
└── MainActivity.kt                # 主活动
```

## 使用说明

1. **启动应用**：打开应用后会显示登录页面
2. **登录**：使用测试账号 `admin` / `123456` 登录
3. **浏览设备**：登录后默认显示设备列表，可以查看所有设备信息
4. **查看设备详情**：点击任意设备可查看详细信息
5. **管理用户信息**：切换到"用户"Tab 查看和编辑个人信息
6. **退出登录**：在用户信息页面点击退出登录按钮

## 模拟数据

应用程序使用模拟数据进行演示：

### 设备数据
- 智能温湿度传感器（在线）
- 智能门锁（在线）
- 智能灯泡（离线）
- 空气质量监测器（故障）

### 用户数据
- 用户名：admin
- 邮箱：<EMAIL>
- 手机号：13800138000

## 开发环境

- Android Studio
- Kotlin
- Gradle 8.11.1
- Android API 24+
- Jetpack Compose BOM 2024.x

## 构建和运行

```bash
# 构建项目
./gradlew assembleDebug

# 安装到设备
./gradlew installDebug
```
